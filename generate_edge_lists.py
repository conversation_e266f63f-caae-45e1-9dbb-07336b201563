import os
import numpy as np
import glob
from tqdm import tqdm

def matrix_to_edge_list(matrix, threshold=0.0):
    """
    将邻接矩阵转换为边列表
    
    参数:
    - matrix: 邻接矩阵 (N x N)
    - threshold: 考虑为连接的阈值，小于此值的连接会被忽略
    
    返回:
    - edge_list: 边列表，每行格式为 [源节点, 目标节点, 权重]
    """
    # 获取矩阵维度
    n = matrix.shape[0]
    edge_list = []
    
    # 遍历矩阵的上三角部分（避免重复边）
    for i in range(n):
        for j in range(i+1, n):
            weight = matrix[i, j]
            # 只保留权重大于阈值的边
            if abs(weight) > threshold:
                # 存储格式：源节点、目标节点、权重
                edge_list.append([i, j, weight])
    
    return np.array(edge_list)

def process_subject_files(file_paths, output_dir, threshold=0.0):
    """
    处理多个被试的矩阵文件，生成边列表

    参数:
    - file_paths: 矩阵文件路径列表
    - output_dir: 输出目录
    - threshold: 边权重阈值
    """
    # 确保输出目录存在
    os.makedirs(output_dir, exist_ok=True)

    # 处理每个文件
    for file_path in tqdm(file_paths, desc="处理被试文件"):
        # 提取被试ID
        filename = os.path.basename(file_path)
        subject_id = filename.split('_knn_')[0]

        # 读取CSV文件
        try:
            # 直接读取CSV矩阵数据
            matrix = np.loadtxt(file_path, delimiter=',')

            if matrix.size == 0:
                print(f"警告: 在文件 {filename} 中找不到矩阵数据")
                continue
            
            # 将矩阵转换为边列表
            edge_list = matrix_to_edge_list(matrix, threshold=threshold)
            
            # 保存为CSV文件
            output_path = os.path.join(output_dir, f"{subject_id}_edges.csv")
            np.savetxt(output_path, edge_list, delimiter=',', fmt='%d,%d,%.6f',
                      header='source,target,weight', comments='')
            
            # 输出一些统计信息
            print(f"被试 {subject_id}: 生成了 {len(edge_list)} 条边")
            
            # 同时保存为适合GCN的数据格式（只有边列表，无权重）
            edge_index = edge_list[:, :2].astype(int)
            edge_attr = edge_list[:, 2].reshape(-1, 1)
            
            # 保存为numpy数组，适合PyTorch Geometric使用
            output_path_npz = os.path.join(output_dir, f"{subject_id}_gcn_data.npz")
            np.savez(output_path_npz, edge_index=edge_index, edge_attr=edge_attr)
            
        except Exception as e:
            print(f"处理文件 {filename} 时出错: {str(e)}")

def main():
    # 设置参数
    threshold = 0.0  # 边权重阈值，小于此值的边将被忽略

    # 获取所有k-NN处理后的矩阵文件
    hc_files = sorted(glob.glob('results/HC/*_knn_*.csv'))
    mci_files = sorted(glob.glob('results/MCI/*_knn_*.csv'))

    print(f"找到 {len(hc_files)} 个HC被试和 {len(mci_files)} 个MCI被试的数据")

    # 处理HC组数据
    print("\n处理HC组数据...")
    process_subject_files(hc_files, 'edge_lists/HC', threshold)

    # 处理MCI组数据
    print("\n处理MCI组数据...")
    process_subject_files(mci_files, 'edge_lists/MCI', threshold)

    print("\n边列表生成完成！")

    # 生成一个汇总文件，记录每个被试属于哪个组（用于标签）
    labels = []

    # HC组标记为0
    for file_path in hc_files:
        subject_id = os.path.basename(file_path).split('_knn_')[0]
        labels.append((subject_id, 0))  # 0表示HC组

    # MCI组标记为1
    for file_path in mci_files:
        subject_id = os.path.basename(file_path).split('_knn_')[0]
        labels.append((subject_id, 1))  # 1表示MCI组
    
    # 保存标签文件
    os.makedirs('edge_lists', exist_ok=True)
    
    # 使用Python文件写入方式替代numpy.savetxt，避免格式问题
    with open('edge_lists/subject_labels.csv', 'w') as f:
        f.write('subject_id,label\n')  # 写入表头
        for subject_id, label in labels:
            f.write(f'{subject_id},{label}\n')
    
    print(f"生成了标签文件，包含 {len(labels)} 个被试的分组信息")
    
    # 生成一个简单的README文件，说明数据格式
    with open('edge_lists/README.txt', 'w') as f:
        f.write("边列表数据说明\n")
        f.write("=============\n\n")
        f.write("每个被试的边列表文件包含以下格式：\n")
        f.write("1. CSV文件 (xxx_edges.csv): 源节点,目标节点,权重\n")
        f.write("2. NPZ文件 (xxx_gcn_data.npz): 包含两个数组：\n")
        f.write("   - edge_index: 形状为[E, 2]的数组，表示边的连接关系\n")
        f.write("   - edge_attr: 形状为[E, 1]的数组，表示边的权重\n\n")
        f.write("subject_labels.csv文件包含每个被试的组别信息：\n")
        f.write("- 0: HC (健康对照组)\n")
        f.write("- 1: MCI (轻度认知障碍组)\n")

if __name__ == "__main__":
    main() 