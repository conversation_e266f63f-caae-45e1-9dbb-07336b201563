# 代码修改说明

## 概述
根据用户要求，修改了 `knn_graph_processing.py` 和 `generate_edge_lists.py` 两个文件，使其适合当前的数据格式：
- HC组和MCI组每个被试22个节点的时间序列数据（CSV格式）
- HC组和MCI组每个被试22×22的功能连接矩阵（CSV格式）

## 主要修改内容

### 1. knn_graph_processing.py 修改

#### 1.1 数据加载函数修改
- **原来**: `load_mat_file()` 函数读取 .mat 文件
- **修改后**: `load_csv_matrix()` 函数读取 CSV 文件
- **具体变化**:
  ```python
  # 原来
  mat_data = sio.loadmat(filepath)
  fc_matrix = mat_data[matrix_var]
  
  # 修改后
  fc_matrix = np.loadtxt(filepath, delimiter=',', skiprows=1, usecols=range(1, 23))
  ```

#### 1.2 文件路径修改
- **原来**: `'data/HC/*.mat'` 和 `'data/MCI/*.mat'`
- **修改后**: `'subject_matrices/HC/*.csv'` 和 `'subject_matrices/MCI/*.csv'`

#### 1.3 被试ID提取修改
- **原来**: `subject_id = os.path.basename(file_path).split('_')[-1].split('.')[0]`
- **修改后**: `subject_id = filename.split('_')[2]`
- **说明**: 从 `ROICorrelation_FisherZ_01001_submatrix.csv` 格式中提取 `01001`

#### 1.4 输出格式修改
- **原来**: 保存为 .mat 文件 `sio.savemat()`
- **修改后**: 保存为 CSV 文件 `np.savetxt()`

#### 1.5 导入库清理
- 移除了不再需要的 `scipy.io as sio`、`sklearn.neighbors.kneighbors_graph`、`scipy.sparse`

### 2. generate_edge_lists.py 修改

#### 2.1 数据加载修改
- **原来**: 读取 .mat 文件并查找矩阵变量
- **修改后**: 直接读取 CSV 文件
- **具体变化**:
  ```python
  # 原来
  mat_data = sio.loadmat(file_path)
  matrix = mat_data['knn_matrix']
  
  # 修改后
  matrix = np.loadtxt(file_path, delimiter=',')
  ```

#### 2.2 文件路径修改
- **原来**: `'results/HC/*_knn_*.mat'` 和 `'results/MCI/*_knn_*.mat'`
- **修改后**: `'results/HC/*_knn_*.csv'` 和 `'results/MCI/*_knn_*.csv'`

#### 2.3 导入库清理
- 移除了不再需要的 `scipy.io as sio`

## 数据格式确认

### 输入数据
1. **时间序列数据**: `selected_timeseries/HC/` 和 `selected_timeseries/MCI/`
   - 格式: CSV文件，包含22个节点的时间序列
   - 示例: `ROISignals_01001_selected.csv`

2. **功能连接矩阵**: `subject_matrices/HC/` 和 `subject_matrices/MCI/`
   - 格式: CSV文件，22×22的功能连接矩阵
   - 示例: `ROICorrelation_FisherZ_01001_submatrix.csv`

### 输出数据
1. **k-NN处理后的矩阵**: `results/HC/` 和 `results/MCI/`
   - 格式: CSV文件
   - 命名: `{subject_id}_knn_{k}.csv`

2. **边列表**: `edge_lists/HC/` 和 `edge_lists/MCI/`
   - 格式: CSV文件和NPZ文件
   - 命名: `{subject_id}_edges.csv` 和 `{subject_id}_gcn_data.npz`

## 测试结果
- ✅ 找到HC组文件: 283个
- ✅ 找到MCI组文件: 213个
- ✅ 矩阵维度正确: 22×22
- ✅ 被试ID提取正确
- ✅ CSV文件加载正常

## 使用方法
1. 运行k-NN图处理: `python knn_graph_processing.py`
2. 生成边列表: `python generate_edge_lists.py`

## 注意事项
- 保持了原有的核心算法逻辑不变
- 只修改了数据输入输出格式
- 确保与22个节点的数据结构兼容
- 所有修改都经过测试验证
