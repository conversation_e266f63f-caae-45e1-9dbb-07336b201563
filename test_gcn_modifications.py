#!/usr/bin/env python3
"""
测试修改后的GCN训练代码是否能正确处理当前数据格式
"""

import os
import glob
import numpy as np
import pandas as pd

def test_timeseries_loading():
    """测试时间序列数据加载"""
    print("=== 测试时间序列数据加载 ===")
    
    # 测试HC组数据
    hc_files = glob.glob('selected_timeseries/HC/*.csv')
    if not hc_files:
        print("错误：找不到HC组的时间序列文件")
        return False
    
    test_file = hc_files[0]
    print(f"测试文件: {test_file}")
    
    try:
        # 按照修改后的方式加载数据
        signals = np.loadtxt(test_file, delimiter=',', skiprows=1, usecols=range(1, 23))
        print(f"时间序列形状: {signals.shape}")
        print(f"数据类型: {signals.dtype}")
        print(f"数据范围: [{signals.min():.4f}, {signals.max():.4f}]")
        
        # 检查是否为正确的维度（时间点 x 22个节点）
        if signals.shape[1] == 22:
            print("✓ 节点数量正确 (22个)")
            return True
        else:
            print(f"✗ 节点数量错误，期望22，实际{signals.shape[1]}")
            return False
            
    except Exception as e:
        print(f"✗ 加载时间序列时出错: {str(e)}")
        return False

def test_edge_lists():
    """测试边列表数据"""
    print("\n=== 测试边列表数据 ===")
    
    # 检查边列表文件
    hc_edge_files = glob.glob('edge_lists/HC/*_gcn_data.npz')
    mci_edge_files = glob.glob('edge_lists/MCI/*_gcn_data.npz')
    
    print(f"HC组边列表文件: {len(hc_edge_files)} 个")
    print(f"MCI组边列表文件: {len(mci_edge_files)} 个")
    
    if len(hc_edge_files) == 0 or len(mci_edge_files) == 0:
        print("错误：边列表文件不存在，请先运行 generate_edge_lists.py")
        return False
    
    # 测试加载一个边列表文件
    test_file = hc_edge_files[0]
    print(f"测试边列表文件: {test_file}")
    
    try:
        npz_data = np.load(test_file)
        edge_index = npz_data['edge_index']
        edge_attr = npz_data['edge_attr']
        
        print(f"边索引形状: {edge_index.shape}")
        print(f"边属性形状: {edge_attr.shape}")
        print(f"节点数量: {edge_index.max() + 1}")
        
        # 检查节点数量是否为22
        if edge_index.max() + 1 == 22:
            print("✓ 边列表中的节点数量正确 (22个)")
            return True
        else:
            print(f"✗ 边列表中的节点数量错误，期望22，实际{edge_index.max() + 1}")
            return False
            
    except Exception as e:
        print(f"✗ 加载边列表时出错: {str(e)}")
        return False

def test_subject_labels():
    """测试被试标签文件"""
    print("\n=== 测试被试标签文件 ===")
    
    label_file = 'edge_lists/subject_labels.csv'
    if not os.path.exists(label_file):
        print("错误：标签文件不存在，请先运行 generate_edge_lists.py")
        return False
    
    try:
        label_df = pd.read_csv(label_file)
        print(f"标签文件形状: {label_df.shape}")
        print(f"列名: {list(label_df.columns)}")
        print(f"前5行:")
        print(label_df.head())
        
        # 检查标签分布
        label_counts = label_df['label'].value_counts()
        print(f"\n标签分布:")
        print(f"HC组 (0): {label_counts.get(0, 0)} 个")
        print(f"MCI组 (1): {label_counts.get(1, 0)} 个")
        
        return True
        
    except Exception as e:
        print(f"✗ 加载标签文件时出错: {str(e)}")
        return False

def test_subject_id_matching():
    """测试被试ID匹配"""
    print("\n=== 测试被试ID匹配 ===")
    
    # 获取边列表文件中的被试ID
    hc_edge_files = glob.glob('edge_lists/HC/*_gcn_data.npz')
    mci_edge_files = glob.glob('edge_lists/MCI/*_gcn_data.npz')
    
    if not hc_edge_files or not mci_edge_files:
        print("错误：边列表文件不存在")
        return False
    
    # 提取被试ID
    edge_subject_ids = []
    for file_path in hc_edge_files[:3] + mci_edge_files[:3]:  # 只测试前几个
        subject_id = os.path.basename(file_path).split('_gcn_data.npz')[0]
        edge_subject_ids.append(subject_id)
        print(f"边列表文件 {os.path.basename(file_path)} -> 被试ID: {subject_id}")
    
    # 检查对应的时间序列文件是否存在
    missing_count = 0
    for subject_id in edge_subject_ids:
        # 检查HC组
        hc_ts_file = f'selected_timeseries/HC/ROISignals_{subject_id}_selected.csv'
        mci_ts_file = f'selected_timeseries/MCI/ROISignals_{subject_id}_selected.csv'
        
        if os.path.exists(hc_ts_file):
            print(f"✓ 找到HC时间序列文件: {hc_ts_file}")
        elif os.path.exists(mci_ts_file):
            print(f"✓ 找到MCI时间序列文件: {mci_ts_file}")
        else:
            print(f"✗ 未找到被试 {subject_id} 的时间序列文件")
            missing_count += 1
    
    if missing_count == 0:
        print("✓ 所有测试的被试ID都能找到对应的时间序列文件")
        return True
    else:
        print(f"✗ 有 {missing_count} 个被试ID找不到对应的时间序列文件")
        return False

def main():
    print("=== 测试修改后的GCN训练代码 ===\n")
    
    tests = [
        ("时间序列数据加载", test_timeseries_loading),
        ("边列表数据", test_edge_lists),
        ("被试标签文件", test_subject_labels),
        ("被试ID匹配", test_subject_id_matching),
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        result = test_func()
        results.append((test_name, result))
        print(f"{test_name}: {'✓ 通过' if result else '✗ 失败'}")
    
    print(f"\n{'='*50}")
    print("测试总结:")
    all_passed = True
    for test_name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"  {test_name}: {status}")
        if not result:
            all_passed = False
    
    if all_passed:
        print("\n🎉 所有测试通过！可以运行GCN训练代码。")
        print("运行命令: python train_gcn_model.py")
    else:
        print("\n❌ 部分测试失败，请检查数据文件。")
        print("确保已经运行了:")
        print("1. python knn_graph_processing.py")
        print("2. python generate_edge_lists.py")

if __name__ == "__main__":
    main()
