#!/usr/bin/env python3
"""
测试修改后的代码是否能正确处理当前数据格式
"""

import numpy as np
import os
import glob

def test_load_csv_matrix():
    """测试CSV矩阵加载函数"""
    # 获取一个示例文件
    hc_files = glob.glob('subject_matrices/HC/*.csv')
    if not hc_files:
        print("错误：找不到HC组的CSV文件")
        return False
    
    test_file = hc_files[0]
    print(f"测试文件: {test_file}")
    
    try:
        # 加载CSV文件，跳过第一行（列名）和第一列（行名）
        fc_matrix = np.loadtxt(test_file, delimiter=',', skiprows=1, usecols=range(1, 23))
        print(f"矩阵形状: {fc_matrix.shape}")
        print(f"矩阵数据类型: {fc_matrix.dtype}")
        print(f"矩阵范围: [{fc_matrix.min():.4f}, {fc_matrix.max():.4f}]")
        
        # 检查是否为22x22矩阵
        if fc_matrix.shape == (22, 22):
            print("✓ 矩阵维度正确 (22x22)")
            return True
        else:
            print(f"✗ 矩阵维度错误，期望(22, 22)，实际{fc_matrix.shape}")
            return False
            
    except Exception as e:
        print(f"✗ 加载矩阵时出错: {str(e)}")
        return False

def test_file_paths():
    """测试文件路径是否正确"""
    hc_files = glob.glob('subject_matrices/HC/*.csv')
    mci_files = glob.glob('subject_matrices/MCI/*.csv')
    
    print(f"找到HC组文件: {len(hc_files)} 个")
    print(f"找到MCI组文件: {len(mci_files)} 个")
    
    if len(hc_files) > 0:
        print(f"HC组示例文件: {hc_files[0]}")
        # 测试被试ID提取
        filename = os.path.basename(hc_files[0])
        subject_id = filename.split('_')[2]
        print(f"提取的被试ID: {subject_id}")
    
    if len(mci_files) > 0:
        print(f"MCI组示例文件: {mci_files[0]}")
        # 测试被试ID提取
        filename = os.path.basename(mci_files[0])
        subject_id = filename.split('_')[2]
        print(f"提取的被试ID: {subject_id}")
    
    return len(hc_files) > 0 and len(mci_files) > 0

def main():
    print("=== 测试修改后的代码 ===\n")
    
    print("1. 测试文件路径...")
    path_ok = test_file_paths()
    print(f"文件路径测试: {'✓ 通过' if path_ok else '✗ 失败'}\n")
    
    print("2. 测试CSV矩阵加载...")
    load_ok = test_load_csv_matrix()
    print(f"CSV加载测试: {'✓ 通过' if load_ok else '✗ 失败'}\n")
    
    if path_ok and load_ok:
        print("🎉 所有测试通过！代码修改成功。")
        print("\n可以运行以下命令来处理数据:")
        print("python knn_graph_processing.py")
        print("python generate_edge_lists.py")
    else:
        print("❌ 测试失败，请检查代码修改。")

if __name__ == "__main__":
    main()
