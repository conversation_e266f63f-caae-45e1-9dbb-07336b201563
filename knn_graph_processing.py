import numpy as np
import matplotlib.pyplot as plt
import os
import glob

def apply_knn_graph(fc_matrix, k=1, mode='connectivity'):
    """
    应用k-NN图处理功能连接矩阵
    
    参数:
    - fc_matrix: 功能连接矩阵
    - k: k-NN的k值
    - mode: 'connectivity'返回0/1矩阵，'distance'返回距离
    
    返回:
    - knn_matrix: k-NN处理后的矩阵
    """
    # 确保矩阵对称
    if not np.allclose(fc_matrix, fc_matrix.T):
        fc_matrix = (fc_matrix + fc_matrix.T) / 2
    
    # 创建一个相同大小的结果矩阵
    n_regions = fc_matrix.shape[0]
    knn_matrix = np.zeros_like(fc_matrix)
    
    # 对于每个节点，找到前k个最强连接
    for i in range(n_regions):
        # 获取当前节点的所有连接强度
        connections = fc_matrix[i, :]
        # 将自连接设为最小值，避免选中
        self_connection = connections[i]
        connections[i] = np.min(connections) - 1
        
        # 找到前k个最强连接（绝对值最大）的索引
        if mode == 'connectivity':
            # 找出最大值
            top_k_indices = np.argsort(connections)[-k:]
            # 设置连接
            knn_matrix[i, top_k_indices] = 1
        else:  # mode == 'distance'
            # 找出最大值
            top_k_indices = np.argsort(connections)[-k:]
            # 设置原始连接强度
            knn_matrix[i, top_k_indices] = fc_matrix[i, top_k_indices]
        
        # 恢复自连接值
        connections[i] = self_connection
    
    # 对称化矩阵
    knn_matrix = np.maximum(knn_matrix, knn_matrix.T)
    
    # 如果mode是connectivity但需要保留原始FC值
    if mode == 'connectivity':
        knn_matrix = knn_matrix * fc_matrix
    
    return knn_matrix

def visualize_matrices(original, processed, title_original="原始FC矩阵", title_processed="k-NN处理后"):
    """可视化原始和处理后的矩阵"""
    fig, axes = plt.subplots(1, 2, figsize=(12, 5))
    
    im0 = axes[0].imshow(original, cmap='viridis', vmin=-1, vmax=1)
    axes[0].set_title(title_original)
    plt.colorbar(im0, ax=axes[0])
    
    im1 = axes[1].imshow(processed, cmap='viridis', vmin=-1, vmax=1)
    axes[1].set_title(title_processed)
    plt.colorbar(im1, ax=axes[1])
    
    plt.tight_layout()
    return fig

def load_csv_matrix(filepath):
    """
    读取CSV文件中的功能连接矩阵

    参数:
    - filepath: CSV文件路径

    返回:
    - fc_matrix: 功能连接矩阵
    """
    try:
        # 加载CSV文件，跳过第一行（列名）和第一列（行名）
        fc_matrix = np.loadtxt(filepath, delimiter=',', skiprows=1, usecols=range(1, 23))

        return fc_matrix

    except Exception as e:
        print(f"读取文件{filepath}时出错: {str(e)}")
        return None

def process_fmri_data(k=1):
    """处理所有fMRI数据"""
    # 获取所有HC和MCI的CSV文件
    hc_files = sorted(glob.glob('subject_matrices/HC/*.csv'))
    mci_files = sorted(glob.glob('subject_matrices/MCI/*.csv'))

    results = []

    # 处理HC数据
    print(f"\n处理HC组数据（共{len(hc_files)}个样本）...")
    for i, file_path in enumerate(hc_files):
        # 从文件名提取被试ID，例如从 ROICorrelation_FisherZ_01001_submatrix.csv 提取 01001
        filename = os.path.basename(file_path)
        subject_id = filename.split('_')[2]  # 获取被试ID部分
        print(f"处理样本 {subject_id} ({i+1}/{len(hc_files)})")

        # 加载功能连接矩阵
        fc_matrix = load_csv_matrix(file_path)
        if fc_matrix is None:
            continue
            
        # 应用k-NN图处理
        processed_matrix = apply_knn_graph(fc_matrix, k=k, mode='connectivity')
        
        # 可视化并保存结果
        fig = visualize_matrices(
            fc_matrix, 
            processed_matrix, 
            f"HC {subject_id} 原始矩阵", 
            f"HC {subject_id} k-NN处理后(k={k})"
        )
        output_dir = 'results/HC'
        os.makedirs(output_dir, exist_ok=True)
        fig.savefig(f"{output_dir}/{subject_id}_knn_{k}.png")
        plt.close(fig)
        
        # 收集结果
        density_original = np.count_nonzero(fc_matrix) / fc_matrix.size
        density_processed = np.count_nonzero(processed_matrix) / processed_matrix.size
        results.append({
            'group': 'HC',
            'subject': subject_id,
            'original_density': density_original,
            'processed_density': density_processed
        })
        
        print(f"  原始FC矩阵密度: {density_original:.4f}")
        print(f"  处理后矩阵密度: {density_processed:.4f}")
        
        # 保存处理后的矩阵
        np.savetxt(f"{output_dir}/{subject_id}_knn_{k}.csv", processed_matrix, delimiter=',')

    # 处理MCI数据
    print(f"\n处理MCI组数据（共{len(mci_files)}个样本）...")
    for i, file_path in enumerate(mci_files):
        # 从文件名提取被试ID
        filename = os.path.basename(file_path)
        subject_id = filename.split('_')[2]  # 获取被试ID部分
        print(f"处理样本 {subject_id} ({i+1}/{len(mci_files)})")

        # 加载功能连接矩阵
        fc_matrix = load_csv_matrix(file_path)
        if fc_matrix is None:
            continue
            
        # 应用k-NN图处理
        processed_matrix = apply_knn_graph(fc_matrix, k=k, mode='connectivity')
        
        # 可视化并保存结果
        fig = visualize_matrices(
            fc_matrix, 
            processed_matrix, 
            f"MCI {subject_id} 原始矩阵", 
            f"MCI {subject_id} k-NN处理后(k={k})"
        )
        output_dir = 'results/MCI'
        os.makedirs(output_dir, exist_ok=True)
        fig.savefig(f"{output_dir}/{subject_id}_knn_{k}.png")
        plt.close(fig)
        
        # 收集结果
        density_original = np.count_nonzero(fc_matrix) / fc_matrix.size
        density_processed = np.count_nonzero(processed_matrix) / processed_matrix.size
        results.append({
            'group': 'MCI',
            'subject': subject_id,
            'original_density': density_original,
            'processed_density': density_processed
        })
        
        print(f"  原始FC矩阵密度: {density_original:.4f}")
        print(f"  处理后矩阵密度: {density_processed:.4f}")

        # 保存处理后的矩阵
        np.savetxt(f"{output_dir}/{subject_id}_knn_{k}.csv", processed_matrix, delimiter=',')
    
    # 输出总结
    print("\n处理结果汇总:")
    print(f"总共处理了 {len(results)} 个样本")
    print(f"平均原始矩阵密度: {np.mean([r['original_density'] for r in results]):.4f}")
    print(f"平均处理后矩阵密度: {np.mean([r['processed_density'] for r in results]):.4f}")

# 主函数演示
def main():
    # 设置k值
    k = 3  # 每个节点保留前k个最强连接
    
    # 处理所有fMRI数据
    process_fmri_data(k=k)

if __name__ == "__main__":
    main()